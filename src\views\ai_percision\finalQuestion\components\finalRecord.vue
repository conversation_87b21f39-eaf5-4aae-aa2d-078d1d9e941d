<template>
    <div class="flex-box">
        <div class="con-box">
            <el-table
                :data="tableData"
                style="width: 100%">
                <el-table-column type="index" label="序号" width="100" />
                <el-table-column prop="subjectName" label="科目" align="left" width="80"></el-table-column>
                <el-table-column prop="title" label="闯关内容" align="left"></el-table-column>
                <el-table-column label="闯关时间" align="left" prop="time"></el-table-column>
                <el-table-column label="正确率" align="left" prop="address">
                    <template #default="scope">
                        <div v-if="isXiaoxue">
                            <div v-if="scope.row.correctRate * 100 > 83" class="flex-img">
                                <span>{{scope.row.correctRate * 100}}% 闯关成功</span>
                                <img src="@/assets/img/percision/final_succ_icon.png" />
                            </div>
                            <span v-else class="red-text">{{scope.row.correctRate * 100}}% 闯关失败</span>
                        </div>
                        <div v-else>
                            <div v-if="scope.row.correctRate == 1" class="flex-img">
                                <span>{{scope.row.correctRate * 100}}% 闯关成功</span>
                                <img src="@/assets/img/percision/final_succ_icon.png" />
                            </div>
                            <span v-else class="red-text">{{scope.row.correctRate * 100}}% 闯关失败</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="查看">
                    <template #default="scope">
                        <span class="blue-text" @click="openDetail(scope.row)">查看详情 ></span>
                    </template>
                </el-table-column>
            </el-table>
            <Pagination
                :total="pageData.total"
                :current="pageData.current"
                :layout="'total, prev, pager, next, jumper'"
                class="page-box"
                @currentSizeChange="currentSizeChange1" />

        </div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { raiseTrainingRecordApi } from "@/api/point"
import { dataDecrypt, dataEncrypt } from "@/utils/secret"
import { useRoute } from 'vue-router'
import { useUserStore } from "@/store/modules/user"
import { storeToRefs } from 'pinia'
import router from '@/router'
import { getDetailsApi } from '@/api/training'
const userStore = useUserStore()

let { subjectObj, learnNow,chapterObj } = storeToRefs(userStore)
const tableData = ref([] as any[])
const route = useRoute()
class IPage {
    total = 0
    current = 1
    size = 10
}
let pageData = reactive(new IPage())
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const loading = ref(false)
const isXiaoxue = learnNow.value.gradeId < 7
onMounted(() => {
    getlist()
})
const getlist = async() => { 
    loading.value = true
    const res: any = await  raiseTrainingRecordApi({
        bookId: subjectObj.value.bookId,
        pointId: queryData.pointId,
        level: queryData.pageSource == '11' ? 5: 6,
        current: pageData.current,
        size: pageData.size
    })
    if(res.code == 200) {
        tableData.value = res.data.records || []
        pageData.total = Number(res.data.total)
    }
    loading.value = false
}
const currentSizeChange1 = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
}
const openDetail = (row) => {
    let url = ''
    if (queryData.pageSource == '11') {
        url = '/ai_percision/final_question/final_training_report'
    } else if (queryData.pageSource == '12') {
        url = '/ai_percision/olympiad_question/olympiad_training_report'
    }
    getDetailsApi({trainingId: row.trainingId}).then((res: any) => {
        if (res.code == 200) {
            const data = res.data
            localStorage.setItem('diagnosticReport', JSON.stringify(data))
            router.push({
                path: url,
                query: {
                    data: dataEncrypt({
                        trainingId: row.trainingId,
                        pageSource: queryData.pageSource,
                        contentType: 'backToRecord'
                    })
                }
            })
        }
    }).catch((error) => {
        console.log(error)
    })
}
</script>
<style lang="scss" scoped>
:deep(.el-table) {
    height: calc(100vh - var(--v3-navigationbar-height) - 168px);
    .cell {
        font-size: 16px;
    }
}
.flex-box {
    display: flex;
    justify-content: center;
    width: 100%;
    background: #f5f5f5;
}
.con-box {
    width: 1300px;
    background-color: white;
    padding: 30px 20px;
}
.flex-img {
    display: flex;
    align-items: center;
    img {
        width: 36px;
        height: 30px;
        margin-left: 6px;
    }
    span {
        font-size: 16px;
    }
}
.red-text {
    color: #dd2a2a;
    font-size: 16px;
    float: left;
}
.blue-text {
    color: #009c7f;
    cursor: pointer;
    font-weight: 400;
}
.page-box {
    margin-top: 20px;
}
</style>