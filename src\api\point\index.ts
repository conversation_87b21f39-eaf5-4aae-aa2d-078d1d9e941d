import { request } from "@/utils/axios"
const SDKPORT = "/api/xiaoyeoo/sdkapi/point"
const PORT = "/api/xiaoyeoo/point"


/** 获取正在学习的章节 */
export function getImprovementPointListApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${SDKPORT}/getImprovementPointList`,
    method: "GET",
    params
  })
}
/** 压轴知识点列表 */
export function getFinalePointApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${PORT}/getFinalePoint`,
    method: "GET",
    params
  })
}
/** 精准练、拔高创建训练(返回训练id) */
export function addTrainingApi(data: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${PORT}/addTraining`,
    method: "POST",
    data
  })
}
/** 拔高保存训练 */
export function saveRaiseTrainingApi(data: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${PORT}/saveRaiseTraining`,
    method: "POST",
    data
  })
}
/** 拔高训练记录 */
export function raiseTrainingRecordApi(params: Object) {
  return request<IApiResponseData<IVersionInfo[]>>({
    url: `${PORT}/raiseTrainingRecord`,
    method: "GET",
    params
  })
}
